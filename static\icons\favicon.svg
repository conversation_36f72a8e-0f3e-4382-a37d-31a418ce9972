<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="newsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6366f1;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#newsGradient)" stroke="#1e40af" stroke-width="1"/>
  
  <!-- Newspaper icon -->
  <rect x="6" y="8" width="20" height="16" rx="2" fill="white" opacity="0.9"/>
  <rect x="8" y="10" width="16" height="2" fill="#3b82f6"/>
  <rect x="8" y="14" width="10" height="1" fill="#6b7280"/>
  <rect x="8" y="16" width="12" height="1" fill="#6b7280"/>
  <rect x="8" y="18" width="8" height="1" fill="#6b7280"/>
  <rect x="20" y="14" width="4" height="6" fill="#e5e7eb"/>
  
  <!-- AI sparkle -->
  <circle cx="24" cy="10" r="2" fill="#fbbf24"/>
  <path d="M24 8 L25 9 L24 10 L23 9 Z" fill="white"/>
</svg>
