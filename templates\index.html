<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>News Feed Pro - AI-Powered News Aggregation</title>
    <meta name="description" content="Modern news aggregation with AI-powered summarization and multi-LLM support">
    <link rel="icon" type="image/svg+xml" href="/static/icons/favicon.svg">
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        /* App-specific layout styles */
        .app-container {
            display: flex;
            min-height: 100vh;
            background-color: var(--bg-secondary);
        }

        .sidebar {
            width: 350px;
            background-color: var(--bg-sidebar);
            color: var(--text-inverse);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
            height: 100vh;
            overflow-y: auto;
            flex-shrink: 0;
            position: sticky;
            top: 0;
            transition: width 0.3s ease, padding 0.3s ease;
        }

        .sidebar.collapsed {
            width: 60px;
            padding: var(--space-4) var(--space-2);
        }

        .sidebar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-8);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--color-gray-700);
            position: relative;
        }

        .sidebar-toggle {
            background: var(--bg-sidebar);
            border: 1px solid var(--color-gray-600);
            color: var(--text-inverse);
            font-size: var(--text-lg);
            cursor: pointer;
            padding: var(--space-2);
            border-radius: var(--radius-md);
            transition: all 0.2s ease;
            position: fixed;
            left: 320px;
            top: 20px;
            box-shadow: var(--shadow-md);
            z-index: 1000;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar-toggle:hover {
            background-color: var(--color-gray-700);
        }

        .sidebar.collapsed .sidebar-toggle {
            left: 30px;
        }

        .sidebar-title {
            font-size: var(--text-xl);
            font-weight: 600;
            color: var(--color-primary);
            margin: 0;
            transition: opacity 0.3s ease;
        }

        .sidebar.collapsed .sidebar-title {
            opacity: 0;
            pointer-events: none;
        }

        .sidebar.collapsed .sidebar-section {
            opacity: 0;
            pointer-events: none;
            height: 0;
            overflow: hidden;
            margin: 0;
            padding: 0;
            border: none;
        }

        .sidebar.collapsed .theme-toggle {
            margin-top: var(--space-4);
        }

        .theme-toggle {
            background: none;
            border: none;
            font-size: var(--text-xl);
            cursor: pointer;
            padding: var(--space-2);
            border-radius: var(--radius-md);
            transition: background-color var(--transition-fast);
        }

        .theme-toggle:hover {
            background-color: var(--color-gray-700);
        }

        .sidebar-section {
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--color-gray-700);
        }

        .sidebar-section:last-child {
            border-bottom: none;
        }

        .sidebar-section h3 {
            font-size: var(--text-lg);
            font-weight: 500;
            color: var(--text-inverse);
            margin: 0 0 var(--space-4) 0;
        }

        .sidebar .form-input,
        .sidebar .form-select {
            background-color: var(--color-gray-700);
            border-color: var(--color-gray-600);
            color: var(--text-inverse);
        }

        .sidebar .form-input:focus,
        .sidebar .form-select:focus {
            border-color: var(--color-primary);
        }

        .sidebar .form-label {
            color: var(--text-inverse);
        }

        .source-item {
            background-color: var(--color-gray-700);
            padding: var(--space-3);
            margin-bottom: var(--space-2);
            border-radius: var(--radius-md);
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: var(--space-2);
        }

        .source-item span {
            flex: 1;
            word-break: break-all;
            font-size: var(--text-sm);
            color: var(--text-inverse);
        }

        .sources-selection {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            padding: var(--space-2);
            background-color: var(--color-gray-800);
        }

        .source-checkbox-item {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-2);
            margin-bottom: var(--space-1);
            border-radius: var(--radius-sm);
            transition: background-color 0.2s ease;
        }

        .source-checkbox-item:hover {
            background-color: var(--color-gray-700);
        }

        .source-checkbox-item input[type="checkbox"] {
            margin: 0;
        }

        .source-checkbox-item label {
            flex: 1;
            margin: 0;
            cursor: pointer;
            font-size: var(--text-sm);
            color: var(--text-inverse);
        }

        .source-checkbox-item .source-info {
            font-size: var(--text-xs);
            color: var(--text-secondary);
            margin-left: 1.5rem;
        }

        .add-source-section {
            margin-top: var(--space-3);
            padding-top: var(--space-3);
            border-top: 1px solid var(--border-primary);
        }

        .add-source-section summary {
            color: var(--text-inverse);
            font-weight: 500;
        }

        .btn-xs {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .main-content {
            flex: 1;
            padding: var(--space-6);
            max-width: calc(100vw - 350px);
            overflow-x: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
            color: var(--text-inverse);
            padding: var(--space-8);
            border-radius: var(--radius-xl);
            margin-bottom: var(--space-6);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header h1 {
            margin: 0 0 var(--space-2) 0;
            font-size: var(--text-4xl);
            font-weight: 700;
        }

        .header p {
            margin: 0;
            font-size: var(--text-lg);
            opacity: 0.9;
        }

        .control-panel {
            background-color: var(--bg-card);
            padding: var(--space-6);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-6);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-primary);
        }

        .control-panel h3 {
            margin: 0 0 var(--space-4) 0;
            color: var(--text-primary);
        }

        .control-actions {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-3);
            margin-bottom: var(--space-4);
        }

        .search-section {
            margin-top: var(--space-4);
            padding-top: var(--space-4);
            border-top: 1px solid var(--border-primary);
        }

        .search-input {
            max-width: 400px;
        }

        .news-container {
            background-color: var(--bg-card);
            padding: var(--space-6);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-primary);
        }

        .news-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--border-primary);
        }

        .news-header h3 {
            margin: 0;
            color: var(--text-primary);
        }

        .filter-controls {
            display: flex;
            gap: var(--space-3);
            align-items: center;
        }

        .pagination-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: var(--space-4);
            margin-top: var(--space-6);
            padding-top: var(--space-4);
            border-top: 1px solid var(--border-primary);
        }

        .page-info {
            font-size: var(--text-sm);
            color: var(--text-secondary);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                position: static;
            }

            .main-content {
                max-width: 100%;
                padding: var(--space-4);
            }

            .header h1 {
                font-size: var(--text-3xl);
            }

            .control-actions {
                flex-direction: column;
            }

            .control-actions .btn {
                width: 100%;
                justify-content: center;
            }

            .news-header {
                flex-direction: column;
                gap: var(--space-4);
                align-items: stretch;
            }

            .filter-controls {
                justify-content: stretch;
            }

            .filter-controls select {
                flex: 1;
            }
        }
    </style>

</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2 class="sidebar-title">⚙️ Settings</h2>
                <button id="themeToggle" class="theme-toggle" title="Toggle theme">
                    <span id="themeIcon">🌙</span>
                </button>
                <button id="sidebarToggle" class="sidebar-toggle" title="Toggle sidebar">
                    <span id="sidebarToggleIcon">◀</span>
                </button>
            </div>

            <!-- News Sources Section -->
            <div class="sidebar-section">
                <h3>📡 News Sources</h3>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="form-label">Select Sources to Fetch:</span>
                        <button onclick="app.toggleAllSources()" class="btn btn-secondary btn-xs" id="toggleAllBtn">
                            Toggle All
                        </button>
                    </div>
                    <div id="sourcesList" class="sources-selection"></div>
                </div>

                <!-- Collapsible Add Source Section -->
                <details class="add-source-section">
                    <summary class="form-label" style="cursor: pointer; margin-bottom: 0.5rem;">
                        ➕ Add New Source
                    </summary>
                    <div class="mb-3">
                        <label for="sourceName" class="form-label">Source Name:</label>
                        <input type="text" id="sourceName" class="form-input" placeholder="e.g., My Tech Blog">
                    </div>
                    <div class="mb-3">
                        <label for="sourceUrl" class="form-label">RSS/API URL:</label>
                        <input type="text" id="sourceUrl" class="form-input" placeholder="e.g., https://example.com/feed.xml">
                    </div>
                    <button onclick="addSource()" class="btn btn-primary btn-sm">
                        ➕ Add Source
                    </button>
                </details>
            </div>

            <!-- Multi-LLM Provider Section -->
            <div class="sidebar-section">
                <h3>🤖 LLM Providers</h3>
                <div id="llmProvidersList" class="mb-4"></div>

                <div class="mb-3">
                    <label for="preferredProvider" class="form-label">Preferred Provider:</label>
                    <select id="preferredProvider" class="form-select">
                        <option value="">Auto (Best Available)</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="preferredModel" class="form-label">Preferred Model:</label>
                    <select id="preferredModel" class="form-select">
                        <option value="">Auto (Default)</option>
                    </select>
                </div>

                <div class="flex gap-2">
                    <button onclick="refreshLLMProviders()" class="btn btn-secondary btn-sm">
                        🔄 Refresh
                    </button>
                    <button onclick="healthCheckProviders()" class="btn btn-secondary btn-sm">
                        🏥 Health Check
                    </button>
                </div>

                <!-- Cost Tracking -->
                <div class="mt-4">
                    <h4 class="text-sm font-semibold mb-2">💰 Cost Tracking</h4>
                    <div id="costTracking" class="text-xs">
                        <div class="mb-1">Daily: <span id="dailyCost">$0.00</span> / <span id="dailyLimit">$10.00</span></div>
                        <div class="mb-1">Monthly: <span id="monthlyCost">$0.00</span> / <span id="monthlyLimit">$200.00</span></div>
                        <div class="budget-bar mt-2">
                            <div id="budgetProgress" class="budget-used" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ollama Model Selection -->
            <div class="sidebar-section">
                <h3>🦙 Ollama Model Selection</h3>
                <div class="mb-3">
                    <label for="ollamaModelSelect" class="form-label">Available Models:</label>
                    <select id="ollamaModelSelect" class="form-select">
                        <option value="">Loading models...</option>
                    </select>
                    <small class="form-text text-muted">Select your preferred Ollama model for summarization</small>
                </div>
                <div class="d-flex gap-2">
                    <button onclick="saveOllamaModel()" class="btn btn-primary btn-sm">
                        💾 Save Model
                    </button>
                    <button onclick="refreshOllamaModels()" class="btn btn-secondary btn-sm">
                        🔄 Refresh
                    </button>
                </div>
                <div id="currentModelInfo" class="mt-2 text-sm text-secondary"></div>
            </div>

            <!-- Filter Section -->
            <div class="sidebar-section">
                <h3>🗂️ Filter Options</h3>
                <div class="mb-3">
                    <label for="categoryFilter" class="form-label">Category:</label>
                    <select id="categoryFilter" class="form-select">
                        <option value="all">All Categories</option>
                    </select>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-content">
                    <h1>🗞️ News Feed Pro</h1>
                    <p>AI-Powered News Aggregation with Multi-LLM Summarization</p>
                </div>
            </header>

            <!-- Control Panel -->
            <section class="control-panel">
                <h3>📊 Control Panel</h3>
                <div class="control-actions">
                    <button class="btn btn-primary" id="fetchBtn">
                        🚀 Fetch & Summarize News
                    </button>
                    <button class="btn btn-secondary" id="loadBtn">
                        📥 Load Latest News
                    </button>
                    <button class="btn btn-secondary" onclick="window.open('/report', '_blank')">
                        📄 View HTML Report
                    </button>
                    <button class="btn btn-warning" id="resetBtn" style="display: none;">
                        🔄 Reset Processing
                    </button>
                </div>

                <!-- Advanced Search Section -->
                <div class="search-section">
                    <div class="search-header">
                        <label for="searchInput" class="form-label">🔍 Advanced Search:</label>
                        <button id="advancedSearchToggle" class="btn btn-secondary btn-sm">
                            ⚙️ Advanced
                        </button>
                    </div>

                    <div class="search-basic">
                        <div class="search-input-container">
                            <input type="text" id="searchInput" class="form-input search-input"
                                   placeholder="Search by title, content, or source..."
                                   autocomplete="off">
                            <div id="searchSuggestions" class="search-suggestions hidden"></div>
                        </div>
                        <div class="search-actions">
                            <button id="searchBtn" class="btn btn-primary btn-sm">
                                🔍 Search
                            </button>
                            <button id="clearSearchBtn" class="btn btn-secondary btn-sm">
                                ✖️ Clear
                            </button>
                        </div>
                    </div>

                    <!-- Advanced Search Panel -->
                    <div id="advancedSearchPanel" class="advanced-search-panel hidden">
                        <div class="search-filters">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <label class="form-label">Sources:</label>
                                    <select id="searchSources" class="form-select" multiple>
                                        <!-- Populated dynamically -->
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label class="form-label">Categories:</label>
                                    <select id="searchCategories" class="form-select" multiple>
                                        <!-- Populated dynamically -->
                                    </select>
                                </div>
                            </div>

                            <div class="filter-row">
                                <div class="filter-group">
                                    <label class="form-label">Date From:</label>
                                    <input type="date" id="searchDateFrom" class="form-input">
                                </div>
                                <div class="filter-group">
                                    <label class="form-label">Date To:</label>
                                    <input type="date" id="searchDateTo" class="form-input">
                                </div>
                            </div>

                            <div class="filter-row">
                                <div class="filter-group">
                                    <label class="form-label">Sentiment:</label>
                                    <select id="searchSentiment" class="form-select">
                                        <option value="">Any</option>
                                        <option value="positive">Positive</option>
                                        <option value="negative">Negative</option>
                                        <option value="neutral">Neutral</option>
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label class="form-label">Sort By:</label>
                                    <select id="searchSortBy" class="form-select">
                                        <option value="relevance">Relevance</option>
                                        <option value="date">Date</option>
                                        <option value="title">Title</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Saved Searches -->
                        <div class="saved-searches-section">
                            <h4 class="text-sm font-semibold mb-2">💾 Saved Searches</h4>
                            <div id="savedSearchesList" class="saved-searches-list"></div>
                            <div class="saved-search-actions">
                                <button id="saveSearchBtn" class="btn btn-secondary btn-sm">
                                    💾 Save Current Search
                                </button>
                                <button id="manageSavedSearchesBtn" class="btn btn-secondary btn-sm">
                                    📋 Manage
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Display -->
                <div id="statusDisplay" class="alert alert-info hidden mt-4">
                    <div class="status-content">
                        <span id="statusText">Ready</span>
                        <button id="statusResetBtn" class="btn btn-warning btn-sm" style="display: none; margin-left: 10px;">
                            🔄 Reset Processing
                        </button>
                    </div>
                </div>
            </section>

            <!-- Insights Dashboard -->
            <section class="insights-dashboard mb-6">
                <div class="insights-header">
                    <h3>📊 Content Insights</h3>
                    <button id="refreshInsightsBtn" class="btn btn-secondary btn-sm">
                        🔄 Refresh
                    </button>
                </div>

                <div class="insights-grid">
                    <!-- Trending Topics -->
                    <div class="insight-card">
                        <h4>🔥 Trending Topics</h4>
                        <div id="trendingTopics" class="trending-list">
                            <p class="text-sm text-secondary">No trending topics found</p>
                        </div>
                    </div>

                    <!-- Sentiment Overview -->
                    <div class="insight-card">
                        <h4>😊 Sentiment Overview</h4>
                        <div id="sentimentOverview" class="sentiment-chart">
                            <p class="text-sm text-secondary">No sentiment data available</p>
                        </div>
                    </div>

                    <!-- Content Stats -->
                    <div class="insight-card">
                        <h4>📈 Content Statistics</h4>
                        <div id="contentStats" class="stats-grid">
                            <p class="text-sm text-secondary">No statistics available</p>
                        </div>
                    </div>

                    <!-- Active Sources -->
                    <div class="insight-card">
                        <h4>📡 Active Sources</h4>
                        <div id="activeSources" class="sources-list">
                            <p class="text-sm text-secondary">No active sources</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- News Container -->
            <section class="news-container">
                <div class="news-header">
                    <h3>📰 Latest News</h3>
                    <div class="filter-controls">
                        <span class="page-info" id="pageInfo">Page 1 of 1</span>
                    </div>
                </div>

                <div id="newsList">
                    <div class="empty-state">
                        <div class="empty-icon">📰</div>
                        <h3>Welcome to News Feed Pro</h3>
                        <p>Click "Load Latest News" to see cached articles or "Fetch & Summarize News" to get fresh content</p>
                        <button onclick="app.loadNews()" class="btn btn-primary">
                            📥 Load Latest News
                        </button>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination-controls">
                    <button id="prevPageBtn" class="btn btn-secondary" disabled>
                        ← Previous
                    </button>
                    <button id="nextPageBtn" class="btn btn-secondary" disabled>
                        Next →
                    </button>
                </div>
            </section>
        </main>
    </div>

    <!-- Load JavaScript -->
    <script src="/static/js/app.js"></script>

    <!-- Legacy function compatibility -->
    <script>
        // Legacy function wrappers for backward compatibility
        function addSource() {
            if (window.app) {
                const name = document.getElementById('sourceName').value;
                const url = document.getElementById('sourceUrl').value;
                if (name && url) {
                    window.app.addSource(name, url);
                } else {
                    window.app.showToast('Please enter both source name and URL', 'warning');
                }
            }
        }

        function saveOllamaModel() {
            if (window.app) {
                window.app.saveOllamaModel();
            }
        }

        function refreshOllamaModels() {
            if (window.app) {
                window.app.populateOllamaModels();
            }
        }

        function editSource(name, url) {
            document.getElementById('sourceName').value = name;
            document.getElementById('sourceUrl').value = url;
        }

        function deleteSource(name) {
            if (window.app && confirm(`Are you sure you want to delete source: ${name}?`)) {
                window.app.deleteSource(name);
            }
        }

        // LLM Provider Management Functions
        function refreshLLMProviders() {
            if (window.app) {
                window.app.refreshLLMProviders();
            }
        }

        function healthCheckProviders() {
            if (window.app) {
                window.app.healthCheckProviders();
            }
        }

        // Sidebar toggle functionality
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const toggleIcon = document.getElementById('sidebarToggleIcon');

            if (sidebar.classList.contains('collapsed')) {
                sidebar.classList.remove('collapsed');
                toggleIcon.textContent = '◀';
                localStorage.setItem('sidebarCollapsed', 'false');
            } else {
                sidebar.classList.add('collapsed');
                toggleIcon.textContent = '▶';
                localStorage.setItem('sidebarCollapsed', 'true');
            }
        }

        // Initialize legacy compatibility
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar toggle event listener
            const sidebarToggle = document.getElementById('sidebarToggle');
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }

            // Restore sidebar state from localStorage
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed');
            if (sidebarCollapsed === 'true') {
                const sidebar = document.getElementById('sidebar');
                const toggleIcon = document.getElementById('sidebarToggleIcon');
                sidebar.classList.add('collapsed');
                toggleIcon.textContent = '▶';
            }

            // Update category filter event listener
            const categoryFilter = document.getElementById('categoryFilter');
            if (categoryFilter) {
                categoryFilter.addEventListener('change', function() {
                    if (window.app) {
                        window.app.displayFilteredNews();
                    }
                });
            }
        });
    </script>
</body>
</html>